//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.qiyuesuo.security.patch.listener.logic;

import com.qiyuesuo.security.patch.helper.VersionCheckHelper;
import com.qiyuesuo.security.patch.qvd.IQvdListenerLogic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;

public class SecurityPatchVersionCheckLogic implements IQvdListenerLogic {
    private static final Logger logger = LoggerFactory.getLogger(SecurityPatchVersionCheckLogic.class);

    public void doQvdListenerLogic(ApplicationReadyEvent event) {
        logger.info("SecurityPatchVersionCheckLogic doQvdListenerLogic");
        String server = event.getApplicationContext().getEnvironment().getProperty("spring.application.name");
        if ("console".equals(server)) {
            try {
                VersionCheckHelper.executeLogic(event.getApplicationContext());
            } catch (Throwable e) {
                logger.info("Encountered an exception while processing the SecurityPatchVersionCheckLogic", e);
            }
        }

    }

    public int getOrder() {
        return 0;
    }
}
