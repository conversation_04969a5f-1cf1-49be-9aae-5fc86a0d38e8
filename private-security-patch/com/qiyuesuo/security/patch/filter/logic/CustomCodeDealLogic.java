//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.code.CustomCode;
import com.qiyuesuo.core.SpringContextHolder;
import com.qiyuesuo.core.Version;
import com.qiyuesuo.core.compiling.javac.JavaDynamicCompiler;
import com.qiyuesuo.security.authc.AuthenticatedToken;
import com.qiyuesuo.security.patch.common.util.CommonUtils;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import net.qiyuesuo.common.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ReflectionUtils;

public class CustomCodeDealLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(CustomCodeDealLogic.class);
    private static final List<String> j = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(j, url) && CommonUtils.a(Version.getServer(), "5.1.0") < 0 && request.getMethod().equalsIgnoreCase("GET") && this.c(request) && this.d(request) && QiyuesuoURIStringUtils.a(request) == null) {
            QiyuesuoURIStringUtils.a(request, 1);
            this.a(request, response);
        }

    }

    private boolean c(HttpServletRequest request) {
        String codeId = request.getParameter("codeId");
        return codeId != null;
    }

    private void a(HttpServletRequest request, HttpServletResponse response) {
        Long codeId = Long.parseLong(request.getParameter("codeId"));
        ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();
        Object customCodeDao = applicationContext.getBean("customCodeDao");
        Method method = ReflectionUtils.findMethod(customCodeDao.getClass(), "selectByPrimaryKey", new Class[]{Object.class});
        method.setAccessible(true);
        Object object = null;

        try {
            object = method.invoke(customCodeDao, codeId);
        } catch (Exception e) {
            logger.error("getCustomCode failed !", e);
        }

        if (object != null) {
            CustomCode customCode = (CustomCode)object;
            ByteArrayInputStream inputStream = null;

            try {
                String renameClassName = JavaDynamicCompiler.renameClassName(new String(customCode.getSourceFile(), "UTF-8"), customCode.getClassName());
                inputStream = new ByteArrayInputStream(renameClassName.getBytes("utf-8"));
                response.setHeader("Content-Disposition", "attachment; fileName=" + customCode.getClassName() + ".java");
                response.setContentType("application/octet-stream");
                IOUtils.writeStream(inputStream, response.getOutputStream());
                response.flushBuffer();
            } catch (Exception var18) {
                Exception e = var18;
                logger.error("下载失败", var18);
                response.setStatus(500);

                try {
                    response.getWriter().write("下载失败: " + e.getMessage());
                } catch (IOException ex) {
                    logger.error("写入错误响应失败", ex);
                }
            } finally {
                IOUtils.safeClose(inputStream);
            }

            logger.warn("完成自主拦截下载逻辑!");
        }

    }

    private boolean d(HttpServletRequest request) {
        ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();

        try {
            Object sessionManager = applicationContext.getBean("sessionManager");
            Method method1 = ReflectionUtils.findMethod(sessionManager.getClass(), "load", new Class[]{HttpServletRequest.class});
            AuthenticatedToken token = (AuthenticatedToken)ReflectionUtils.invokeMethod(method1, sessionManager, new Object[]{request});
            return token != null;
        } catch (Exception e) {
            logger.error("权限校验失败！", e);
            return false;
        }
    }

    static {
        j.addAll(SecurityResourceOperator.e("CustomCodeDealLogic.URL_LIST"));
        logger.info("注册CustomCodeDealLogic拦截器成功!");
    }
}
