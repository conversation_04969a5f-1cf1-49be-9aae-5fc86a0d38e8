//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.filter.wrapper.NetcheckRequestWrapper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NetCheckPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(NetCheckPreventLogic.class);
    private static final List<String> m = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(m, url)) {
            NetcheckRequestWrapper requestWrapper = new NetcheckRequestWrapper(request);
            if (QiyuesuoURIStringUtils.a(request) == null) {
                QiyuesuoURIStringUtils.a(request, 1);
                filterChain.doFilter(requestWrapper, response);
            }
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("NetCheckPreventLogic.URI"));
        logger.info("注册NetCheckPrevent拦截器成功！将对NetCheckPrevent进行拦截！");
    }
}
