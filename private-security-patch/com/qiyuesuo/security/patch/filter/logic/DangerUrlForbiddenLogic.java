//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.core.Version;
import com.qiyuesuo.security.patch.common.util.CommonUtils;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DangerUrlForbiddenLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(DangerUrlForbiddenLogic.class);
    private static final List<String> m = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.b(m, url) && CommonUtils.a(Version.getServer(), "5.1.0") < 0) {
            logger.warn("匹配到攻击:{}", url);
            SecurityResponseHelper.a(response, url);
            QiyuesuoURIStringUtils.a(request, 1);
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("DangerUrlForbiddenLogic.RISK_URI"));
        logger.info("注册DangerUrlForbiddenLogic拦截器成功！将对ForbiddenUrl进行拦截！");
    }
}
