//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.logback.ApplicationContextHolder;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DangerOpenUrlLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(DangerOpenUrlLogic.class);
    private static final List<String> m = new ArrayList();
    private static final String n = SecurityResourceOperator.f("DangerOpenUrlLogic.RISK_STRING");
    private static final String o = SecurityResourceOperator.f("DangerOpenUrlLogic.PARAM_NAME");

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        String paramName = request.getHeader(o);
        if (this.j("open") && QiyuesuoURIStringUtils.b(m, url) && n.equals(paramName)) {
            logger.warn("匹配到攻击:{}", url);
            SecurityResponseHelper.a(response, url);
            QiyuesuoURIStringUtils.a(request, 1);
        }

    }

    private boolean j(String platform) {
        return platform.equals(ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("spring.application.name"));
    }

    static {
        m.addAll(SecurityResourceOperator.e("DangerOpenUrlLogic.RISK_URI"));
        logger.info("注册DangerOpenUrlLogic拦截器成功！将对OpenUrl进行拦截！");
    }
}
