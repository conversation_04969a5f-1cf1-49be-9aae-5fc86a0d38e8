//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.core.SpringContextHolder;
import com.qiyuesuo.core.Version;
import com.qiyuesuo.security.authc.AuthenticatedToken;
import com.qiyuesuo.security.core.SecurityContextHolder;
import com.qiyuesuo.security.patch.common.util.CommonUtils;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.filter.wrapper.CustomCodeRequestWrapper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ReflectionUtils;

public class CustomCodePreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(CustomCodePreventLogic.class);
    private static final List<String> k = new ArrayList();
    private static String l = SecurityResourceOperator.f("CustomCodePreventLogic.METHOD");

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.b(k, url)) {
            if (!this.d(request) && CommonUtils.a(Version.getServer(), "5.1.0") < 0) {
                logger.warn("匹配到攻击:用户权限校验不通过，无法上传！");
                SecurityResponseHelper.a(response, url);
                QiyuesuoURIStringUtils.a(request, 1);
                return;
            }

            CustomCodeRequestWrapper requestWrapper = new CustomCodeRequestWrapper(request);
            if (QiyuesuoURIStringUtils.a(request) == null) {
                QiyuesuoURIStringUtils.a(request, 1);
                filterChain.doFilter(requestWrapper, response);
            }
        }

    }

    private boolean d(HttpServletRequest request) {
        ApplicationContext applicationContext = SpringContextHolder.getApplicationContext();
        if ("webapp".equals(applicationContext.getEnvironment().getProperty("spring.application.name"))) {
            try {
                Object sessionManager = applicationContext.getBean("sessionManager");
                Method method1 = ReflectionUtils.findMethod(sessionManager.getClass(), "load", new Class[]{HttpServletRequest.class});
                AuthenticatedToken token = (AuthenticatedToken)ReflectionUtils.invokeMethod(method1, sessionManager, new Object[]{request});
                SecurityContextHolder.getContext().setAuthenticationToken(token);
                Object userController = applicationContext.getBean("userController");
                Object targetController = AopProxyUtils.getSingletonTarget(userController);
                Method method = ReflectionUtils.findMethod(userController.getClass(), l);
                method.setAccessible(true);
                boolean re = (Boolean)method.invoke(targetController);
                if (re) {
                    return true;
                }
            } catch (Exception e) {
                logger.warn("权限校验异常！", e);
            }

            return false;
        } else {
            return true;
        }
    }

    static {
        k.addAll(SecurityResourceOperator.e("CustomCodePreventLogic.UPLOAD_URL_LIST"));
        logger.info("注册CustomCodePrevent拦截器成功！将对CustomCode进行拦截！");
    }
}
