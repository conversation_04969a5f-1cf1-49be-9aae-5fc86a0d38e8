//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.core.Version;
import com.qiyuesuo.core.cache.SimpleCacheClient;
import com.qiyuesuo.logback.ApplicationContextHolder;
import com.qiyuesuo.security.patch.common.util.CommonUtils;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

public class SweepCodePreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(SweepCodePreventLogic.class);
    private static final String v = SecurityResourceOperator.g("SweepCodePreventLogic.OVER_434_REGEX").trim();
    private static final String w = SecurityResourceOperator.f("SweepCodePreventLogic.METHOD_1");
    private static final String x = SecurityResourceOperator.f("SweepCodePreventLogic.METHOD_2");
    private static final Pattern y;
    private static final Boolean s;
    private static final List<String> m;
    private static final List<String> z;
    private static final String A;
    private static SimpleCacheClient B;

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        String queryString = request.getQueryString();
        if (s && this.j("webapp") && QiyuesuoURIStringUtils.b(z, url) && this.e() && CommonUtils.a(Version.getServer(), "4.3.4") >= 0 && y.matcher(queryString).matches()) {
            ReflectionUtils.invokeMethod(ReflectionUtils.findMethod(SimpleCacheClient.class, w, new Class[]{String.class, Serializable.class, Integer.TYPE}), B, new Object[]{A, A, 600});
        }

        if (s && this.j("webapp") && QiyuesuoURIStringUtils.b(m, url) && this.e()) {
            Object result = ReflectionUtils.invokeMethod(ReflectionUtils.findMethod(SimpleCacheClient.class, x, new Class[]{String.class}), B, new Object[]{A});
            if (result == null) {
                logger.warn("匹配到攻击:{}", url);
                SecurityResponseHelper.a(response, url);
                QiyuesuoURIStringUtils.a(request, 1);
            }
        }

    }

    public static List<String> d() {
        String[] versions = new String[]{"4.3.8", "4.3.9", "4.4.1", "4.4.2", "4.4.3", "4.4.4", "4.4.5", "4.4.6", "4.4.8", "4.4.9", "5.0.0", "5.0.1", "5.0.2", "5.0.3", "5.0.4"};
        return Arrays.asList(versions);
    }

    public boolean e() {
        if (B == null) {
            if (ApplicationContextHolder.getApplicationContext() != null && ApplicationContextHolder.getApplicationContext().getBean("simpleCacheClient") != null) {
                B = (SimpleCacheClient)ApplicationContextHolder.getApplicationContext().getBean("simpleCacheClient");
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    private boolean j(String platform) {
        return platform.equals(ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("spring.application.name"));
    }

    static {
        y = Pattern.compile(v);
        s = d().contains(Version.getServer());
        m = new ArrayList();
        z = new ArrayList();
        A = SweepCodePreventLogic.class.getName();
        m.addAll(SecurityResourceOperator.e("SweepCodePreventLogic.RISK_URI"));
        z.addAll(SecurityResourceOperator.e("SweepCodePreventLogic.PRE_URI"));
        logger.info("注册SweepCode拦截器成功！将对SweepCode进行拦截！");
    }
}
