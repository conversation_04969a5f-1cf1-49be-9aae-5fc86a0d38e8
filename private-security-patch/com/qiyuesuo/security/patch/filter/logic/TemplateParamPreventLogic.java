//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.filter.wrapper.TemplateParamRequestWrapper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TemplateParamPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(TemplateParamPreventLogic.class);
    private static final List<String> C = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.b(C, url)) {
            TemplateParamRequestWrapper servletRequestWrapper = new TemplateParamRequestWrapper(request);
            if (QiyuesuoURIStringUtils.a(request) == null) {
                QiyuesuoURIStringUtils.a(request, 1);
                filterChain.doFilter(servletRequestWrapper, response);
            }
        }

    }

    static {
        C.addAll(SecurityResourceOperator.e("TemplateParamRequestWrapper.TEMPLATE_PARAM_URI"));
        C.addAll(SecurityResourceOperator.e("TemplateParamRequestWrapper.TEMPLATE_HTML_URI"));
        logger.info("注册templateParamPrevent拦截器成功！将对CustomCode进行拦截！");
    }
}
