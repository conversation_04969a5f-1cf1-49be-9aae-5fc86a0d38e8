//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.logback.ApplicationContextHolder;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.helper.VersionCheckHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UpgradePreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(UpgradePreventLogic.class);
    private static final List<String> p = new ArrayList();
    private static final List<String> E = new ArrayList();
    private static String F;

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.b(p, url) && !QiyuesuoURIStringUtils.a(E, url) && !"true".equals(F) && !this.j("open")) {
            logger.info("拦截的url: {}", url);
            SecurityResponseHelper.a(response, url);
        }

    }

    private boolean j(String platform) {
        return platform.equals(ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("spring.application.name"));
    }

    static {
        p.addAll(SecurityResourceOperator.e("UpgradePreventLogic.RISK_UPGRADE_URI"));
        E.addAll(SecurityResourceOperator.e("UpgradePreventLogic.RISK_UPGRADE_EXCLUDE_URI"));
        logger.info("注册upgrade拦截器成功！将对upgrade,update相关url进行拦截！");

        try {
            VersionCheckHelper.executeLogic(ApplicationContextHolder.getApplicationContext());
            F = ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("qiyuesuo.upgrade.manual");
        } catch (Throwable e) {
            logger.error("上报对象初始化失败！", e);
        }

    }
}
