//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PinCatchPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(PinCatchPreventLogic.class);
    private static final List<String> m = new ArrayList();
    private static final String t = SecurityResourceOperator.f("PinCatchPreventLogic.NAME");

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(m, url)) {
            request.setCharacterEncoding("UTF-8");
            String value = request.getParameter(t);
            if (Boolean.parseBoolean(value)) {
                logger.warn("匹配到攻击:{}", t);
                QiyuesuoURIStringUtils.a(request, 1);
                SecurityResponseHelper.a(response, url);
            }
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("PinCatchPreventLogic.RISK_URI"));
        logger.info("注册PinCatchPreventLogic拦截器成功！");
    }
}
