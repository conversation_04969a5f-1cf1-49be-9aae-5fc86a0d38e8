//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PinLoginPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(PinLoginPreventLogic.class);
    private static final List<String> m = new ArrayList();
    private static final List<String> u = new ArrayList();
    private static final String t = SecurityResourceOperator.f("PinLoginPreventLogic.NAME");

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(m, url)) {
            request.setCharacterEncoding("UTF-8");
            String service = request.getParameter(t);
            boolean validate = true;
            if (service != null && !"".equals(service)) {
                for(String param : u) {
                    if (!service.contains(param)) {
                        validate = false;
                    }
                }

                if (validate) {
                    logger.warn("匹配到攻击:{}", service);
                    QiyuesuoURIStringUtils.a(request, 1);
                    SecurityResponseHelper.a(response, url);
                }
            }
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("PinLoginPreventLogic.RISK_URI"));
        u.addAll(SecurityResourceOperator.e("PinLoginPreventLogic.PARAM"));
        logger.info("注册PinLoginPreventLogic拦截器成功！");
    }
}
