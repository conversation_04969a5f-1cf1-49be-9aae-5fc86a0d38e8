//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

public class DangerUrlPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(DangerUrlPreventLogic.class);
    private static final String d = "^(?!.*\\/\\/)(?!.*\\.\\/)(?!.*[;]).*$";
    private static final Pattern e = Pattern.compile("^(?!.*\\/\\/)(?!.*\\.\\/)(?!.*[;]).*$");
    private static final List<String> m = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        boolean matches = e.matcher(url).matches();
        if (!matches) {
            logger.warn("匹配到攻击:{ }", url);
            SecurityResponseHelper.a(response, url);
        }

        if (QiyuesuoURIStringUtils.b(m, url)) {
            request.setCharacterEncoding("UTF-8");
            String path = request.getParameter("path");
            if (!StringUtils.isEmpty(path) && !e.matcher(path).matches()) {
                logger.warn("匹配到攻击:{}", path);
                SecurityResponseHelper.a(response, url);
            }
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("DangerUrlPreventLogic.RISK_URI"));
        logger.info("注册DangerUrl拦截器成功！将对dangerUrl进行拦截！");
    }
}
