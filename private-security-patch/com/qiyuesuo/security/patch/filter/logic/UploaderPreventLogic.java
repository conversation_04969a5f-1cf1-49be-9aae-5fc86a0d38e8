//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.filter.wrapper.UploaderPreventWrapper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UploaderPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(UploaderPreventLogic.class);
    private static final String d = "html";
    private static final Pattern e = Pattern.compile("html");
    private static final List<String> m = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.a(m, url)) {
            UploaderPreventWrapper requestWrapper = new UploaderPreventWrapper(request);
            if (QiyuesuoURIStringUtils.a(request) == null) {
                QiyuesuoURIStringUtils.a(request, 1);
                filterChain.doFilter(requestWrapper, response);
            }
        }

    }

    static {
        m.addAll(SecurityResourceOperator.e("UploaderPreventLogic.RISK_URI"));
        logger.info("注册DangerUrl拦截器成功！将对dangerUrl进行拦截！");
    }
}
