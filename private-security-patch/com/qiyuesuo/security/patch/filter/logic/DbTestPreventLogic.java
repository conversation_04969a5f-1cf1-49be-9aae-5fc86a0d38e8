//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.logback.ApplicationContextHolder;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DbTestPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(DbTestPreventLogic.class);
    private static final List<String> p = new ArrayList();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.b(p, url) && this.j("console") && this.b()) {
            logger.info("拦截的url: {}", url);
            SecurityResponseHelper.a(response, url);
            QiyuesuoURIStringUtils.a(request, 1);
        }

    }

    private boolean j(String platform) {
        return platform.equals(ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("spring.application.name"));
    }

    public boolean b() {
        String step = ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("qiyuesuo.setup");
        return "complete".equals(step);
    }

    static {
        p.addAll(SecurityResourceOperator.e("DbTestPreventLogic.RISK_UPGRADE_URI"));
        logger.info("注册DbTestPreventLogic拦截器成功！将对相关url进行拦截！");
    }
}
