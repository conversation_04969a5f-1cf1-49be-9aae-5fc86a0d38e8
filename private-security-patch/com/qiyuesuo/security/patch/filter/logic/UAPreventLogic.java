//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.core.config.GlobalProperties;
import com.qiyuesuo.core.config.GlobalPropertiesCache;
import com.qiyuesuo.logback.ApplicationContextHolder;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.SecurityResponseHelper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

public class UAPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(UAPreventLogic.class);
    private static final List<String> m = new ArrayList();
    private static final String l = SecurityResourceOperator.f("UAPreventLogic.METHOD");
    private static GlobalPropertiesCache D;

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(m, url)) {
            request.setCharacterEncoding("UTF-8");
            if (!this.f()) {
                logger.warn("安全拦截！不允许访问！！");
                QiyuesuoURIStringUtils.a(request, 1);
                SecurityResponseHelper.a(response, url);
            }
        }

    }

    private boolean f() {
        if (D == null) {
            if (ApplicationContextHolder.getApplicationContext() == null || ApplicationContextHolder.getApplicationContext().getBean("globalPropertiesCache") == null) {
                return true;
            }

            D = (GlobalPropertiesCache)ApplicationContextHolder.getApplicationContext().getBean("globalPropertiesCache");
        }

        GlobalProperties globalProperties = D.get();
        if (globalProperties != null) {
            if (!"webapp".equals(ApplicationContextHolder.getApplicationContext().getEnvironment().getProperty("spring.application.name"))) {
                return true;
            }

            Method method = ReflectionUtils.findMethod(GlobalProperties.class, l);
            if (method != null) {
                return (Boolean)ReflectionUtils.invokeMethod(method, globalProperties);
            }
        }

        return true;
    }

    static {
        m.addAll(SecurityResourceOperator.e("UAPreventLogic.RISK_URI"));
        logger.info("注册UAPreventLogic拦截器成功！");
    }
}
