//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.logic;

import com.qiyuesuo.core.Version;
import com.qiyuesuo.security.patch.common.util.CommonUtils;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.filter.wrapper.PdfverifierPreventWrapper;
import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PdfverifierPreventLogic implements IQvdFilterLogic {
    private static final Logger logger = LoggerFactory.getLogger(PdfverifierPreventLogic.class);
    private static final List<String> r = new ArrayList();
    private static final Boolean s = c();

    public void doQvdLogic(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String url = QiyuesuoURIStringUtils.b(request);
        if (QiyuesuoURIStringUtils.c(r, url) && s && request.getMethod().equalsIgnoreCase("POST")) {
            PdfverifierPreventWrapper servletRequestWrapper = new PdfverifierPreventWrapper(request);
            if (QiyuesuoURIStringUtils.a(request) == null) {
                QiyuesuoURIStringUtils.a(request, 1);
                filterChain.doFilter(servletRequestWrapper, response);
            }
        }

    }

    public static boolean c() {
        return CommonUtils.a(Version.getServer(), "4.3.8") >= 0;
    }

    static {
        r.addAll(SecurityResourceOperator.e("PdfverifierPreventLogic.PDFVERIFIER_URL_LIST"));
        logger.info("注册PdfverifierPreventLogic拦截器成功!");
    }
}
