//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import javax.mail.internet.MimeUtility;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.Part;
import net.qiyuesuo.common.file.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ContentDisposition;

public class PdfverifierPreventWrapper extends HttpServletRequestWrapper {
    private static final Logger logger = LoggerFactory.getLogger(PdfverifierPreventWrapper.class);
    private static final String d = SecurityResourceOperator.f("PdfverifierPreventWrapper.TYPE");
    private static final String N = SecurityResourceOperator.f("PdfverifierPreventWrapper.PARAM");
    private static final List<String> G = new ArrayList();
    private static final List<String> O = new ArrayList();

    public PdfverifierPreventWrapper(HttpServletRequest request) throws IOException {
        super(request);
    }

    public Collection<Part> getParts() throws ServletException, IOException {
        Collection<Part> parts = super.getParts();
        String url = QiyuesuoURIStringUtils.b((HttpServletRequest)super.getRequest());
        List<Part> newParts = new ArrayList();

        for(Part part : parts) {
            String headerValue = part.getHeader("Content-Disposition");
            ContentDisposition disposition = ContentDisposition.parse(headerValue);
            String filename = disposition.getFilename();
            if (filename != null) {
                if (filename.startsWith("=?") && filename.endsWith("?=")) {
                    filename = PdfverifierPreventWrapper.MimeDelegate.n(filename);
                }

                String fileType = FileUtils.getExtName(filename);
                if ("pdf".equalsIgnoreCase(fileType) && !QiyuesuoURIStringUtils.c(O, url)) {
                    newParts.add(part);
                } else {
                    try (InputStream in = part.getInputStream()) {
                        byte[] ofdBytes = IOUtils.toByteArray(in);
                        if (this.b(new ByteArrayInputStream(ofdBytes))) {
                            logger.warn("检测到穿越符！文件名称为{}", filename);
                        } else {
                            newParts.add(part);
                        }
                    } catch (IOException e) {
                        logger.error("解析流失败！", e);
                    }
                }
            } else {
                newParts.add(part);
            }
        }

        return newParts;
    }

    private boolean b(InputStream inputStream) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            while((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();
                if (entryName.contains(N)) {
                    logger.warn("检测到文件名称异常！完整名称为：{}", entryName);
                    boolean var23 = true;
                    return var23;
                }

                for(String key : G) {
                    if (entryName.toLowerCase().endsWith(key)) {
                        logger.warn("检测到文件名称异常！文件名称为{}", entryName);
                        boolean var8 = true;
                        return var8;
                    }
                }
            }

            return false;
        } catch (Throwable e) {
            logger.warn("检测到文件名称异常！不是合法的ofd文件！", e);
            return false;
        }
    }

    static {
        G.addAll(SecurityResourceOperator.e("PdfverifierPreventWrapper.OTHER"));
        O.addAll(SecurityResourceOperator.e("PdfverifierPreventWrapper.SENSITIVE_URL"));
    }

    private static class MimeDelegate {
        public static String n(String value) {
            try {
                return MimeUtility.decodeText(value);
            } catch (UnsupportedEncodingException ex) {
                throw new IllegalStateException(ex);
            }
        }
    }
}
