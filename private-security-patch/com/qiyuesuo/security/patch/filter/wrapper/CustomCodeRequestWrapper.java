//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.Part;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CustomCodeRequestWrapper extends HttpServletRequestWrapper {
    private static final Logger logger = LoggerFactory.getLogger(CustomCodeRequestWrapper.class);
    private static final List<String> G = new ArrayList();
    private static final String H = SecurityResourceOperator.f("CustomCodeRequestWrapper.IMPORT_REGEX");
    private static final String I = SecurityResourceOperator.f("CustomCodeRequestWrapper.MULTIPLE_WHITESPACE_REGEX");
    private static final String J = SecurityResourceOperator.f("CustomCodeRequestWrapper.ADJACENT_SEMICOLON_REGEX");

    public CustomCodeRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
    }

    public Collection<Part> getParts() throws ServletException, IOException {
        Collection<Part> sourceParts = super.getParts();
        Collection<Part> newParts = new ArrayList();

        for(Part sourcePart : sourceParts) {
            String bodyData = a(sourcePart.getInputStream());
            if (!QiyuesuoURIStringUtils.a(G, bodyData) && k(bodyData)) {
                newParts.add(sourcePart);
            } else {
                logger.warn("监测到敏感字符！");
            }
        }

        return newParts;
    }

    public static String a(InputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = null;
        String result = null;

        try {
            reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
            String line = "";

            while((line = reader.readLine()) != null) {
                sb.append(line);
            }

            result = StringEscapeUtils.unescapeJava(sb.toString());
        } catch (IOException e) {
            logger.error("代码检测失败！", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.error("代码检测失败！", e);
                }
            }

            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error("代码检测失败！", e);
                }
            }

        }

        return result;
    }

    public static boolean k(String code) {
        if (Pattern.compile(J).matcher(code).find()) {
            return false;
        } else {
            String[] statements = code.split(";");

            for(String statement : statements) {
                statement = statement.trim();
                if (!statement.isEmpty() && statement.startsWith("import")) {
                    if (Pattern.compile(I + "{2,}").matcher(statement).find()) {
                        return false;
                    }

                    if (!l(statement + ";")) {
                        return false;
                    }
                }
            }

            return true;
        }
    }

    private static boolean l(String statement) {
        if (statement.contains("*")) {
            return false;
        } else {
            Pattern pattern = Pattern.compile(H);
            Matcher matcher = pattern.matcher(statement);
            return matcher.matches();
        }
    }

    static {
        G.addAll(SecurityResourceOperator.e("CustomCodeRequestWrapper.SENSITIVE_KEY_LISTS"));
    }
}
