//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import java.io.UnsupportedEncodingException;
import javax.mail.internet.MimeUtility;

class PdfverifierPreventWrapper$MimeDelegate {
    private PdfverifierPreventWrapper$MimeDelegate() {
    }

    public static String n(String value) {
        try {
            return MimeUtility.decodeText(value);
        } catch (UnsupportedEncodingException ex) {
            throw new IllegalStateException(ex);
        }
    }
}
