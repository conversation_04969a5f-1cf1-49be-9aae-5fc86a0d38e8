//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

class TemplateParamRequestWrapper$TemplateParamDto {
    private String expression;

    TemplateParamRequestWrapper$TemplateParamDto(TemplateParamRequestWrapper this$0) {
        this.this$0 = this$0;
    }

    public String getExpression() {
        return this.expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public String toString() {
        return "TemplateParamDto{expression='" + this.expression + '\'' + '}';
    }
}
