//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

class TemplateParamRequestWrapper$TemplateParamExtensionParamDto {
    private String extensionParam;

    TemplateParamRequestWrapper$TemplateParamExtensionParamDto(TemplateParamRequestWrapper this$0) {
        this.this$0 = this$0;
    }

    public String getExtensionParam() {
        return this.extensionParam;
    }

    public void setExtensionParam(String extensionParam) {
        this.extensionParam = extensionParam;
    }

    public String toString() {
        return "TemplateParamDto{expression='" + this.extensionParam + '\'' + '}';
    }
}
