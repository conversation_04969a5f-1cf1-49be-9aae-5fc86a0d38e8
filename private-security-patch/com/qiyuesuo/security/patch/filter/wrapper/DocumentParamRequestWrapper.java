//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qiyuesuo.security.patch.common.util.TemplateParamUtils;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

public class DocumentParamRequestWrapper extends HttpServletRequestWrapper {
    private static final Logger logger = LoggerFactory.getLogger(DocumentParamRequestWrapper.class);
    private Map<String, String[]> K = new HashMap();
    private static ObjectMapper objectMapper = new ObjectMapper();
    private static final Pattern f = Pattern.compile("\\{.*?\\}");

    public DocumentParamRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        Map<String, String[]> requestMap = request.getParameterMap();
        this.K.putAll(requestMap);
    }

    public ServletInputStream getInputStream() throws IOException {
        String header = super.getHeader("Content-Type");
        if (!StringUtils.isEmpty(header) && header.contains("application/json")) {
            String json = IOUtils.toString(super.getInputStream(), "utf-8");
            if (StringUtils.isEmpty(json)) {
                return super.getInputStream();
            } else if (!json.contains("expression")) {
                return new ParameterServletInputStream(new ByteArrayInputStream(json.getBytes("utf-8")));
            } else {
                ByteArrayInputStream bis;
                try {
                    JsonNode rootNode = objectMapper.readTree(json);
                    boolean isValid = this.validateExpressions(rootNode);
                    if (!isValid) {
                        bis = new ByteArrayInputStream("".getBytes("utf-8"));
                    } else {
                        bis = new ByteArrayInputStream(objectMapper.writeValueAsString(rootNode).getBytes("utf-8"));
                    }
                } catch (Exception e) {
                    logger.info("监测到敏感字符失败，error：{}", e);
                    bis = new ByteArrayInputStream("".getBytes("utf-8"));
                }

                return new ParameterServletInputStream(bis);
            }
        } else {
            return super.getInputStream();
        }
    }

    private boolean validateExpressions(JsonNode rootNode) throws JsonProcessingException {
        Queue<JsonNode> queue = new LinkedList();
        queue.add(rootNode);

        while(!queue.isEmpty()) {
            JsonNode node = (JsonNode)queue.poll();
            if (node.isObject()) {
                Iterator<Map.Entry<String, JsonNode>> fields = node.fields();

                while(fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = (Map.Entry)fields.next();
                    JsonNode fieldValue = (JsonNode)field.getValue();
                    if (((String)field.getKey()).equals("extensionParam") && fieldValue.isTextual()) {
                        Map<String, Object> mapExtensionParam = (Map)objectMapper.readValue(fieldValue.asText(), Map.class);
                        String expression = (String)mapExtensionParam.get("expression");
                        if (!StringUtils.isEmpty(expression)) {
                            String replacedStr = TemplateParamUtils.h(expression);
                            if (!TemplateParamUtils.i(replacedStr)) {
                                logger.warn("监测到敏感字符！,eval 表达式：{},模拟转换后为：{}", expression, replacedStr);
                                return false;
                            }
                        }
                    } else if (((String)field.getKey()).equals("expression") && fieldValue.isTextual()) {
                        String expression = fieldValue.asText();
                        if (!StringUtils.isEmpty(expression)) {
                            String replacedStr = TemplateParamUtils.h(expression);
                            if (!TemplateParamUtils.i(replacedStr)) {
                                logger.warn("监测到敏感字符！,eval 表达式：{},模拟转换后为：{}", expression, replacedStr);
                                return false;
                            }
                        }
                    } else {
                        queue.add(fieldValue);
                    }
                }
            } else if (node.isArray()) {
                node.forEach(queue::add);
            }
        }

        return true;
    }

    public String[] m(String name) {
        return (String[])this.K.get(name);
    }

    private static String h(String input) {
        Matcher matcher = f.matcher(input);
        StringBuffer sb = new StringBuffer();

        while(matcher.find()) {
            matcher.appendReplacement(sb, "0");
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    class ParameterServletInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public ParameterServletInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        public boolean isFinished() {
            return true;
        }

        public boolean isReady() {
            return true;
        }

        public void setReadListener(ReadListener listener) {
        }

        public int read() throws IOException {
            return this.bis.read();
        }
    }
}
