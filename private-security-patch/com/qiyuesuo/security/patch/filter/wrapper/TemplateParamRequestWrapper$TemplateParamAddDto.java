//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import java.util.List;

class TemplateParamRequestWrapper$TemplateParamAddDto {
    private List<TemplateParamRequestWrapper.TemplateParamExtensionParamDto> params;

    TemplateParamRequestWrapper$TemplateParamAddDto(TemplateParamRequestWrapper this$0) {
        this.this$0 = this$0;
    }

    private List<TemplateParamRequestWrapper.TemplateParamExtensionParamDto> getParams() {
        return this.params;
    }

    private void setParams(List<TemplateParamRequestWrapper.TemplateParamExtensionParamDto> params) {
        this.params = params;
    }

    public String toString() {
        return "TemplateParamEditDto{params=" + this.params + '}';
    }
}
