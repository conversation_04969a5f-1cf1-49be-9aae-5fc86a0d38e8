//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import net.qiyuesuo.common.lang.StringUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UploaderPreventWrapper extends HttpServletRequestWrapper {
    private static final Logger logger = LoggerFactory.getLogger(UploaderPreventWrapper.class);
    private static final String d = SecurityResourceOperator.f("UploaderPreventLogic.REGEX");
    private static final String N = SecurityResourceOperator.f("UploaderPreventLogic.PARAM");
    private Map<String, String[]> K = new HashMap();
    private static ObjectMapper objectMapper = new ObjectMapper();

    public UploaderPreventWrapper(HttpServletRequest request) throws IOException {
        super(request);
        Map<String, String[]> requestMap = request.getParameterMap();
        this.K.putAll(requestMap);
    }

    public ServletInputStream getInputStream() throws IOException {
        String header = super.getHeader("Content-Type");
        if (StringUtils.isBlank(header)) {
            return super.getInputStream();
        } else if (!header.contains("application/json")) {
            return super.getInputStream();
        } else {
            String json = IOUtils.toString(super.getInputStream(), "utf-8");
            if (org.springframework.util.StringUtils.isEmpty(json)) {
                return super.getInputStream();
            } else {
                ByteArrayInputStream bis;
                try {
                    Map map = (Map)objectMapper.readValue(json, Map.class);
                    if (map.get(d) != null && N.equals(((String)map.get(d)).toLowerCase())) {
                        logger.warn("监测到非法上传类型！,非法的类型：{}", map.get(d));
                        bis = new ByteArrayInputStream("".getBytes("utf-8"));
                    } else {
                        bis = new ByteArrayInputStream(objectMapper.writeValueAsString(map).getBytes("utf-8"));
                    }
                } catch (Exception e) {
                    logger.info("监测到非法上传类型，error：{}", e);
                    bis = new ByteArrayInputStream(json.getBytes("utf-8"));
                }

                return new ParameterServletInputStream(bis);
            }
        }
    }

    public String[] m(String name) {
        return (String[])this.K.get(name);
    }

    class ParameterServletInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public ParameterServletInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        public boolean isFinished() {
            return true;
        }

        public boolean isReady() {
            return true;
        }

        public void setReadListener(ReadListener listener) {
        }

        public int read() throws IOException {
            return this.bis.read();
        }
    }
}
