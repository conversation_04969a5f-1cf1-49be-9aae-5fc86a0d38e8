//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qiyuesuo.security.patch.common.util.QiyuesuoURIStringUtils;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import com.qiyuesuo.security.patch.common.util.TemplateParamUtils;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

public class TemplateParamRequestWrapper extends HttpServletRequestWrapper {
    private static final Logger logger = LoggerFactory.getLogger(TemplateParamRequestWrapper.class);
    private static final String TEMPLATE_PARAM_EDITS_ERROR_REGX = "[a-zA-Z]\\.[a-zA-Z]";
    private static final List<String> TEMPLATE_PARAM_URI = new ArrayList();
    private static final List<String> TEMPLATE_HTML_URI = new ArrayList();
    private static ObjectMapper objectMapper = new ObjectMapper();

    public TemplateParamRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
    }

    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public ServletInputStream getInputStream() throws IOException {
        String url = QiyuesuoURIStringUtils.b(this);
        if (!QiyuesuoURIStringUtils.b(TEMPLATE_PARAM_URI, url) && !QiyuesuoURIStringUtils.b(TEMPLATE_HTML_URI, url)) {
            return super.getInputStream();
        } else {
            String requestBody = getBodyString(super.getInputStream());
            ByteArrayInputStream bis = new ByteArrayInputStream(requestBody.getBytes(StandardCharsets.UTF_8));
            if (!StringUtils.isEmpty(requestBody)) {
                try {
                    JsonNode rootNode = objectMapper.readTree(requestBody);
                    boolean isValid = this.validateExpressions(rootNode);
                    if (!isValid) {
                        bis = new ByteArrayInputStream("".getBytes("utf-8"));
                    } else {
                        bis = new ByteArrayInputStream(objectMapper.writeValueAsString(rootNode).getBytes("utf-8"));
                    }
                } catch (Exception e) {
                    logger.info("监测到敏感字符失败，error：{}", e);
                    bis = new ByteArrayInputStream("".getBytes("utf-8"));
                }
            }

            return new DangerParamInputStream(bis);
        }
    }

    public static String getBodyString(ServletInputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = null;

        try {
            reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
            String line = "";

            while((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }

        return sb.toString();
    }

    private boolean validateExpressions(JsonNode rootNode) throws JsonProcessingException {
        Queue<JsonNode> queue = new LinkedList();
        queue.add(rootNode);

        while(!queue.isEmpty()) {
            JsonNode node = (JsonNode)queue.poll();
            if (node.isObject()) {
                Iterator<Map.Entry<String, JsonNode>> fields = node.fields();

                while(fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = (Map.Entry)fields.next();
                    JsonNode fieldValue = (JsonNode)field.getValue();
                    if (((String)field.getKey()).equals("extensionParam") && fieldValue.isTextual()) {
                        Map<String, Object> mapExtensionParam = (Map)objectMapper.readValue(fieldValue.asText(), Map.class);
                        String expression = (String)mapExtensionParam.get("expression");
                        if (!StringUtils.isEmpty(expression)) {
                            String replacedStr = TemplateParamUtils.h(expression);
                            if (!TemplateParamUtils.i(replacedStr)) {
                                logger.warn("监测到敏感字符！,eval 表达式：{},模拟转换后为：{}", expression, replacedStr);
                                return false;
                            }
                        }
                    } else if (((String)field.getKey()).equals("expression") && fieldValue.isTextual()) {
                        String expression = fieldValue.asText();
                        if (!StringUtils.isEmpty(expression)) {
                            String replacedStr = TemplateParamUtils.h(expression);
                            if (!TemplateParamUtils.i(replacedStr)) {
                                logger.warn("监测到敏感字符！,eval 表达式：{},模拟转换后为：{}", expression, replacedStr);
                                return false;
                            }
                        }
                    } else {
                        queue.add(fieldValue);
                    }
                }
            } else if (node.isArray()) {
                node.forEach(queue::add);
            }
        }

        return true;
    }

    static {
        TEMPLATE_PARAM_URI.addAll(SecurityResourceOperator.e("TemplateParamRequestWrapper.TEMPLATE_PARAM_URI"));
        TEMPLATE_HTML_URI.addAll(SecurityResourceOperator.e("TemplateParamRequestWrapper.TEMPLATE_HTML_URI"));
    }

    class TemplateParamEditDto {
        private List<TemplateParamDto> params;

        private List<TemplateParamDto> getParams() {
            return this.params;
        }

        private void setParams(List<TemplateParamDto> params) {
            this.params = params;
        }

        public String toString() {
            return "TemplateParamEditDto{params=" + this.params + '}';
        }
    }

    class TemplateParamDto {
        private String expression;

        public String getExpression() {
            return this.expression;
        }

        public void setExpression(String expression) {
            this.expression = expression;
        }

        public String toString() {
            return "TemplateParamDto{expression='" + this.expression + '\'' + '}';
        }
    }

    class TemplateParamAddDto {
        private List<TemplateParamExtensionParamDto> params;

        private List<TemplateParamExtensionParamDto> getParams() {
            return this.params;
        }

        private void setParams(List<TemplateParamExtensionParamDto> params) {
            this.params = params;
        }

        public String toString() {
            return "TemplateParamEditDto{params=" + this.params + '}';
        }
    }

    class TemplateParamExtensionParamDto {
        private String extensionParam;

        public String getExtensionParam() {
            return this.extensionParam;
        }

        public void setExtensionParam(String extensionParam) {
            this.extensionParam = extensionParam;
        }

        public String toString() {
            return "TemplateParamDto{expression='" + this.extensionParam + '\'' + '}';
        }
    }

    class DangerParamInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public DangerParamInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        public boolean isFinished() {
            return false;
        }

        public boolean isReady() {
            return true;
        }

        public void setReadListener(ReadListener listener) {
        }

        public int read() throws IOException {
            return this.bis.read();
        }
    }
}
