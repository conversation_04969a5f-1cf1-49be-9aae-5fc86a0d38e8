//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.filter.wrapper;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;

class DocumentParamRequestWrapper$ParameterServletInputStream extends ServletInputStream {
    private ByteArrayInputStream bis;

    public DocumentParamRequestWrapper$ParameterServletInputStream(DocumentParamRequestWrapper this$0, ByteArrayInputStream bis) {
        this.L = this$0;
        this.bis = bis;
    }

    public boolean isFinished() {
        return true;
    }

    public boolean isReady() {
        return true;
    }

    public void setReadListener(ReadListener listener) {
    }

    public int read() throws IOException {
        return this.bis.read();
    }
}
