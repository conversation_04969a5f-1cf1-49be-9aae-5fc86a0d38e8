//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.config;

import org.springframework.context.ApplicationContext;

public class ApplicationContextHolder {
    private static ApplicationContext g;

    public static void setApplicationContext(ApplicationContext applicationContext) {
        g = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return g;
    }
}
