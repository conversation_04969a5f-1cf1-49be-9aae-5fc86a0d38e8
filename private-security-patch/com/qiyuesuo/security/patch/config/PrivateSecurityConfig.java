//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PrivateSecurityConfig {
    private static final Logger logger = LoggerFactory.getLogger(PrivateSecurityConfig.class);

    static {
        logger.info("契约锁安全补丁包挂载成功！，挂载安全补丁包版本为：{}", "2.1.5");
    }
}
