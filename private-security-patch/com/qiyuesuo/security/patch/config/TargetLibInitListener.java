//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.qiyuesuo.security.patch.config;

import com.qiyuesuo.security.patch.qvd.IQvdFilterLogic;
import com.qiyuesuo.security.patch.qvd.logic.QvdLogicManager;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

@Component
public class TargetLibInitListener implements ApplicationContextAware, ApplicationListener<ApplicationReadyEvent>, Ordered {
    private static final Logger logger = LoggerFactory.getLogger(TargetLibInitListener.class);
    @Autowired
    private QvdLogicManager qvdLogicManager;

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextHolder.setApplicationContext(applicationContext);
    }

    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        Map<String, IQvdFilterLogic> qvdBeans = applicationReadyEvent.getApplicationContext().getBeansOfType(IQvdFilterLogic.class);
        if (null != qvdBeans) {
            for(Map.Entry<String, IQvdFilterLogic> cell : qvdBeans.entrySet()) {
                logger.info("已加载QVDBean,beanName=>{},className=>{}", cell.getKey(), ((IQvdFilterLogic)cell.getValue()).getClass().getName());
            }
        } else {
            logger.info("未加载任何QVDBean");
        }

        this.a();
    }

    private void a() {
        this.qvdLogicManager.status();
    }

    public int getOrder() {
        return -100;
    }
}
