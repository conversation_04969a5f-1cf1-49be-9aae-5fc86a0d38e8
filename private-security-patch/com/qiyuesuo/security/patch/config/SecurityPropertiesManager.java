//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.config;

import com.qiyuesuo.security.SecurityProperties;
import com.qiyuesuo.security.patch.common.util.SecurityResourceOperator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SecurityPropertiesManager {
    private static final Logger logger = LoggerFactory.getLogger(SecurityPropertiesManager.class);
    private static final List<String> h = new ArrayList();
    private static final List<String> i = new ArrayList();

    public static void a(SecurityProperties securityProperties) {
        String[] allowPatterns = securityProperties.getAllowedPatterns();
        if (null != securityProperties && null != allowPatterns) {
            List<String> allows = (List)Arrays.stream(allowPatterns).collect(Collectors.toList());
            List<String> targetAllows = new ArrayList();
            logger.info("登录过滤器中的规则有{}个", allowPatterns.length);

            for(String cell : allows) {
                boolean hit = false;

                for(String target : h) {
                    if (cell.endsWith(target)) {
                        hit = true;
                        logger.info("向登录过滤url规则中，移出url：{}", cell);
                    }
                }

                if (!hit) {
                    targetAllows.add(cell);
                }
            }

            for(String cell : i) {
                if (!targetAllows.contains(cell)) {
                    targetAllows.add(cell);
                }
            }

            logger.warn("登录过滤器中的新的规则数量有{}个", targetAllows.size());
            securityProperties.setAllowedPatterns((String[])targetAllows.toArray(new String[0]));
        } else {
            logger.warn("登录过滤器配置规则为空，不操作");
        }

    }

    static {
        h.addAll(SecurityResourceOperator.e("SecurityPropertiesManager.RISK_URL_LISTS"));
        i.addAll(SecurityResourceOperator.e("SecurityPropertiesManager.ADDITIONAL_URL_LISTS"));
        logger.info("SecurityPropertiesManager.RISK_URL_LISTS size {}", h.size());
        logger.info("SecurityPropertiesManager.ADDITIONAL_URL_LISTS size {}", i.size());
    }
}
