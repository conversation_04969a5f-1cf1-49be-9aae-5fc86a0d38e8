//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.cloud.req;

public class CloudVersionCheckReq {
    private String token;
    private String identifier;
    private String patchVersion;
    private String privVersion;
    private String privVersionInfo;
    private Boolean customized;
    private String customizedCode;

    public CloudVersionCheckReq() {
    }

    public CloudVersionCheckReq(String token, String identifier, String patchVersion, String privVersion, String privVersionInfo, Boolean customized, String customizedCode) {
        this.token = token;
        this.identifier = identifier;
        this.patchVersion = patchVersion;
        this.privVersion = privVersion;
        this.privVersionInfo = privVersionInfo;
        this.customized = customized;
        this.customizedCode = customizedCode;
    }

    public String getToken() {
        return this.token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getIdentifier() {
        return this.identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getPatchVersion() {
        return this.patchVersion;
    }

    public void setPatchVersion(String patchVersion) {
        this.patchVersion = patchVersion;
    }

    public String getPrivVersion() {
        return this.privVersion;
    }

    public void setPrivVersion(String privVersion) {
        this.privVersion = privVersion;
    }

    public String getPrivVersionInfo() {
        return this.privVersionInfo;
    }

    public void setPrivVersionInfo(String privVersionInfo) {
        this.privVersionInfo = privVersionInfo;
    }

    public Boolean getCustomized() {
        return this.customized;
    }

    public void setCustomized(Boolean customized) {
        this.customized = customized;
    }

    public String getCustomizedCode() {
        return this.customizedCode;
    }

    public void setCustomizedCode(String customizedCode) {
        this.customizedCode = customizedCode;
    }

    public String toString() {
        return "CloudVersionCheckReq{token='" + this.token + '\'' + ", identifier='" + this.identifier + '\'' + ", patchVersion='" + this.patchVersion + '\'' + ", privVersion='" + this.privVersion + '\'' + ", privVersionInfo='" + this.privVersionInfo + '\'' + ", customized=" + this.customized + ", customizedCode='" + this.customizedCode + '\'' + '}';
    }
}
