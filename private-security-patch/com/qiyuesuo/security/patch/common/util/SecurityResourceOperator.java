//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import com.qiyuesuo.security.encrypt.RSAUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.jar.JarFile;
import java.util.zip.ZipEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SecurityResourceOperator {
    private static final Map<String, List<String>> b = new HashMap();
    private static final Logger logger = LoggerFactory.getLogger(SecurityResourceOperator.class);

    public static List<String> e(String key) {
        return (List)b.get(key);
    }

    public static String f(String key) {
        return (String)((List)b.get(key)).get(0);
    }

    public static String g(String key) {
        return new String(Base64.getDecoder().decode((String)((List)b.get(key)).get(0)));
    }

    static {
        try {
            URI jarFileUri = SecurityResourceOperator.class.getProtectionDomain().getCodeSource().getLocation().toURI();
            String jarFilePath = (new File(jarFileUri)).getPath();
            JarFile jarFile = new JarFile(jarFilePath);
            ZipEntry zipentry = jarFile.getEntry("security.rsc");

            try (InputStream inputStream = jarFile.getInputStream(zipentry)) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                Throwable var7 = null;

                try {
                    label270:
                    while(true) {
                        String line;
                        if ((line = reader.readLine()) == null) {
                            Iterator var50 = b.entrySet().iterator();

                            while(true) {
                                if (!var50.hasNext()) {
                                    break label270;
                                }

                                Map.Entry<String, List<String>> entry = (Map.Entry)var50.next();
                                String key = (String)entry.getKey();
                                List<String> valueList = (List)entry.getValue();
                                logger.debug("Key: {}", key);
                                logger.debug("Value: {}", valueList);
                            }
                        }

                        String[] parts = line.split(":", 2);
                        if (parts.length == 2) {
                            String key = parts[0].trim();
                            String value = parts[1].trim();
                            List<String> decodedValueList = new ArrayList();
                            String[] encodedValueList = value.split(",");

                            for(String encodeValue : encodedValueList) {
                                String decodeValue = RSAUtils.decryptByDefaultPrivateKey(encodeValue);
                                decodedValueList.add(decodeValue.trim());
                            }

                            String decodedKey = RSAUtils.decryptByDefaultPrivateKey(key);
                            b.put(decodedKey, decodedValueList);
                        }
                    }
                } catch (Throwable var44) {
                    var7 = var44;
                    throw var44;
                } finally {
                    if (reader != null) {
                        if (var7 != null) {
                            try {
                                reader.close();
                            } catch (Throwable var43) {
                                var7.addSuppressed(var43);
                            }
                        } else {
                            reader.close();
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        logger.info("安全补丁包的url已经被加载SecurityResourceOperator,共加载个数：{}", b.size());
    }
}
