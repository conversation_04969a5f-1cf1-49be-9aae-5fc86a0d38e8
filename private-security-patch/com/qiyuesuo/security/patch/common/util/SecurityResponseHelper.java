//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SecurityResponseHelper {
    private static final Logger logger = LoggerFactory.getLogger(SecurityResponseHelper.class);
    private static ObjectMapper objectMapper = new ObjectMapper();
    private static final String c = "UTF-8";

    public void a(HttpServletResponse response, Object data) throws IOException {
        response.setStatus(200);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().write(objectMapper.writeValueAsString(data));
    }

    public static void a(HttpServletResponse response, String url) throws IOException {
        response.setStatus(403);
        Map<String, String> resultMap = new HashMap();
        resultMap.put("msg", "您的请求属于非法访问，请检查");
        resultMap.put("url", url);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().write(objectMapper.writeValueAsString(resultMap));
    }

    public void b(HttpServletResponse response, String url) throws IOException {
        response.setStatus(403);
        Map<String, String> resultMap = new HashMap();
        resultMap.put("msg", "您的请求数据中存在攻击数据，请检查");
        resultMap.put("url", url);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().write(objectMapper.writeValueAsString(resultMap));
    }

    public void c(HttpServletResponse response, String code) throws IOException {
        response.setStatus(403);
        Map<String, String> resultMap = new HashMap();
        resultMap.put("msg", "上传代码中包含敏感字符串，请检查！");
        resultMap.put("code", code);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().write(objectMapper.writeValueAsString(resultMap));
    }
}
