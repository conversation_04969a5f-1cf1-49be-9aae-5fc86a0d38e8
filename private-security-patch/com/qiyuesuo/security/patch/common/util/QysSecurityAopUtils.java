//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.WebApplicationContext;

public class QysSecurityAopUtils {
    private static final Logger logger = LoggerFactory.getLogger(QysSecurityAopUtils.class);

    public static boolean d(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (Exception var2) {
            return false;
        }
    }

    public static Object b(String typeName, String fieldName) {
        Object result = null;

        try {
            Class authModeEnumClass = Class.forName(typeName);
            Field field = authModeEnumClass.getField(fieldName);
            result = field.get((Object)null);
            return result;
        } catch (Exception e) {
            logger.error("execute constructor failed!", e);
            throw new RuntimeException("安全拦截方法执行失败");
        }
    }

    public static Object a(String typeName, Class[] params, Object[] args) {
        Object result = null;

        try {
            Class clazz = Class.forName(typeName);
            Constructor constructor = null;
            if (null != params && 0 != params.length) {
                constructor = clazz.getDeclaredConstructor(params);
            } else {
                constructor = clazz.getDeclaredConstructor();
            }

            constructor.setAccessible(true);
            if (null != args && 0 != args.length) {
                result = constructor.newInstance(args);
            } else {
                result = constructor.newInstance();
            }

            return result;
        } catch (Exception e) {
            logger.error("execute constructor failed!", e);
            throw new RuntimeException("安全拦截方法执行失败");
        }
    }

    public static Object a(String typeName, String methodName, Class[] params, Object target, Object[] args) {
        Object result = null;

        try {
            Class clazz = Class.forName(typeName);
            Method method = null;
            if (null != params && 0 != params.length) {
                method = clazz.getMethod(methodName, params);
            } else {
                method = clazz.getMethod(methodName);
            }

            method.setAccessible(true);
            if (null != args && 0 != args.length) {
                result = method.invoke(target, args);
            } else {
                result = method.invoke(target);
            }

            return result;
        } catch (Exception e) {
            logger.error("execute bean method failed!", e);
            throw new RuntimeException("安全拦截方法执行失败");
        }
    }

    public static Object a(ApplicationContext context, String typeName, String methodName, Class[] params, Object[] args) {
        Object result = null;

        try {
            Class clazz = Class.forName(typeName);
            Object bean = context.getBean(clazz);
            if (null != bean) {
                Method method = null;
                if (null != params && 0 != params.length) {
                    method = clazz.getMethod(methodName, params);
                } else {
                    method = clazz.getMethod(methodName);
                }

                method.setAccessible(true);
                if (null != args && 0 != args.length) {
                    result = method.invoke(bean, args);
                } else {
                    result = method.invoke(bean);
                }
            }

            return result;
        } catch (Exception e) {
            logger.error("execute bean method failed!", e);
            throw new RuntimeException("安全拦截方法执行失败");
        }
    }

    public static Object a(WebApplicationContext context, String typeName, String methodName, Class[] params, Object[] args) {
        Object result = null;

        try {
            Class clazz = Class.forName(typeName);
            Object bean = context.getBean(clazz);
            if (null != bean) {
                Method method = null;
                if (null != params && 0 != params.length) {
                    method = clazz.getDeclaredMethod(methodName, params);
                } else {
                    method = clazz.getDeclaredMethod(methodName);
                }

                method.setAccessible(true);
                if (null != args && 0 != args.length) {
                    result = method.invoke(bean, args);
                } else {
                    result = method.invoke(bean);
                }
            }

            return result;
        } catch (Exception e) {
            logger.error("execute bean method failed!", e);
            throw new RuntimeException("安全拦截方法执行失败");
        }
    }
}
