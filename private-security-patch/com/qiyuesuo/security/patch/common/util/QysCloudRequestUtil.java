//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QysCloudRequestUtil {
    private static final Logger logger = LoggerFactory.getLogger(QysCloudRequestUtil.class);
    private static ObjectMapper objectMapper = new ObjectMapper();

    public static String a(String cloudService, String uri, Object req) throws Exception {
        if (null == req) {
            return null;
        } else {
            HttpURLConnection urlConnection = null;
            URL url = new URL(cloudService + uri);
            urlConnection = (HttpURLConnection)url.openConnection();
            urlConnection.setConnectTimeout(3000);
            urlConnection.setReadTimeout(600000);
            urlConnection.setUseCaches(false);
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);
            urlConnection.setRequestMethod("POST");
            urlConnection.setRequestProperty("content-type", "application/json; charset=utf-8");
            String body = null;

            String var9;
            try (PrintWriter printWriter = new PrintWriter(urlConnection.getOutputStream())) {
                printWriter.print(objectMapper.writeValueAsString(req));
                printWriter.flush();
                int responseCode = urlConnection.getResponseCode();
                if (responseCode != 200) {
                    throw new Exception(cloudService + uri + "请求连接失败,请优先确认网络连接是否正常 code: " + responseCode);
                }

                body = a(urlConnection);
                var9 = body;
            }

            return var9;
        }
    }

    public static String a(URLConnection urlConnection) {
        String result = null;

        try (InputStream inputStream = urlConnection.getInputStream()) {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            Throwable var5 = null;

            try {
                StringBuilder response = new StringBuilder();

                String line;
                while((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }

                result = response.toString();
                logger.debug("返回信息：{}", result);
            } catch (Throwable var31) {
                var5 = var31;
                throw var31;
            } finally {
                if (bufferedReader != null) {
                    if (var5 != null) {
                        try {
                            bufferedReader.close();
                        } catch (Throwable var30) {
                            var5.addSuppressed(var30);
                        }
                    } else {
                        bufferedReader.close();
                    }
                }

            }
        } catch (Exception ex) {
            logger.debug("解析返回信息失败!", ex);
        }

        return result;
    }

    public static String c(String json) throws Exception {
        JsonNode root = null;
        root = objectMapper.readTree(json);
        JsonNode resultNode = root.get("code");
        String resultJson = resultNode.toString();
        return resultJson;
    }
}
