//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

public class QiyuesuoURIStringUtils {
    private static final String a = "myFilterAlreadyExecuted";

    public static Object a(HttpServletRequest request) {
        return request.getAttribute("myFilterAlreadyExecuted");
    }

    public static void a(HttpServletRequest request, int value) {
        request.setAttribute("myFilterAlreadyExecuted", value);
    }

    public static String b(HttpServletRequest request) throws IOException {
        String uri = request.getRequestURI();
        uri = b(uri);
        String contextPath = request.getContextPath();
        if (!StringUtils.isEmpty(contextPath)) {
            uri = uri.replaceFirst(contextPath, "");
        }

        return a(uri);
    }

    public static String a(String uri) {
        return uri.contains("//") ? uri.replace("//", "/") : uri;
    }

    public static String b(String uri) throws IOException {
        return URLDecoder.decode(uri, "UTF-8");
    }

    public static boolean a(List<String> list, String str) {
        for(String keyword : list) {
            if (str.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    public static boolean b(List<String> list, String str) {
        for(String keyword : list) {
            if (str.startsWith(keyword)) {
                return true;
            }
        }

        return false;
    }

    public static boolean c(List<String> list, String str) {
        for(String keyword : list) {
            if (str.equals(keyword)) {
                return true;
            }
        }

        return false;
    }
}
