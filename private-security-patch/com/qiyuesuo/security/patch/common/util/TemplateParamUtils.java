//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TemplateParamUtils {
    private static final String d = SecurityResourceOperator.f("TemplateParamUtils.REGEX");
    private static final Pattern e;
    private static final Pattern f;

    public static String h(String input) {
        Matcher matcher = f.matcher(input);
        StringBuffer sb = new StringBuffer();

        while(matcher.find()) {
            matcher.appendReplacement(sb, "0");
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    public static boolean i(String replacedStr) {
        return e.matcher(replacedStr).matches();
    }

    static {
        e = Pattern.compile(d);
        f = Pattern.compile(SecurityResourceOperator.f("TemplateParamUtils.PATTERN"));
    }
}
