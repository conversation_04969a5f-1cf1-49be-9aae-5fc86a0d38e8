//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.qiyuesuo.security.patch.helper;

import com.qiyuesuo.core.Version;
import com.qiyuesuo.core.config.GlobalPropertiesCache;
import com.qiyuesuo.core.config.TokenProperties;
import com.qiyuesuo.security.patch.cloud.req.CloudVersionCheckReq;
import com.qiyuesuo.security.patch.common.util.QysCloudRequestUtil;
import com.qiyuesuo.security.patch.common.util.QysSecurityAopUtils;
import net.qiyuesuo.common.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

public class VersionCheckHelper {
    private static final Logger logger = LoggerFactory.getLogger(VersionCheckHelper.class);
    private static final String CHECK_VERSION_URL_PREFIX = "https://gw.qiyuesuo.";
    private static final String CHECK_VERSION_ADDRESS = "/priv/checkVersion";

    public static void executeLogic(ApplicationContext applicationContext) {
        try {
            if (applicationContext == null) {
                return;
            }

            initialCheck(applicationContext);
            CloudVersionCheckReq params = buildParameter(applicationContext);
            String serviceUrl = getServiceUrl(applicationContext);
            QysCloudRequestUtil.a(serviceUrl, "/priv/checkVersion", params);
            logger.info("上报版本号信息成功！{}", "2.1.5");
        } catch (Throwable e) {
            logger.error("上报安全补丁包版本信息失败!", e);
        }

    }

    private static CloudVersionCheckReq buildParameter(ApplicationContext applicationContext) throws Exception {
        String privVersion = Version.getServer();
        return privVersion.startsWith("2") ? buildParameter("com.qiyuesuo.core.license.LicenseUtil", "com.qiyuesuo.core.license.License", applicationContext) : buildParameter("com.qiyuesuo.license.LicenseUtil", "com.qiyuesuo.license.License", applicationContext);
    }

    private static CloudVersionCheckReq buildParameter(String licenseUtilBeanPath, String licenseObjectPath, ApplicationContext applicationContext) throws Exception {
        Object license = QysSecurityAopUtils.a(applicationContext, licenseUtilBeanPath, "getFromFile", new Class[0], new Object[0]);
        if (license == null) {
            throw new Exception("license 缓存不存在");
        } else {
            String privVersion = Version.getServer();
            String privVersionInfo = Version.getServer() + "." + Version.getDate();
            Boolean customized = Version.getDate().contains(".");
            String customizedCode = customized ? Version.getDate().split("\\.")[0] : "";
            String token = (String)QysSecurityAopUtils.a(licenseObjectPath, "getToken", new Class[0], license, new Object[0]);
            String identifier = (String)QysSecurityAopUtils.a(licenseObjectPath, "getIdentifier", new Class[0], license, new Object[0]);
            CloudVersionCheckReq req = new CloudVersionCheckReq(token, identifier, "2.1.5", privVersion, privVersionInfo, customized, customizedCode);
            return req;
        }
    }

    private static String getServiceUrl(ApplicationContext applicationContext) {
        String privVersion = Version.getServer();
        if (!privVersion.startsWith("2")) {
            Object license = QysSecurityAopUtils.a(applicationContext, "com.qiyuesuo.license.LicenseUtil", "getFromFile", new Class[0], new Object[0]);
            Object licenseType = QysSecurityAopUtils.a("com.qiyuesuo.license.License", "getLicenseType", new Class[0], license, new Object[0]);
            return "https://gw.qiyuesuo." + licenseType.toString().toLowerCase();
        } else {
            String qysServiceUrl = (String)QysSecurityAopUtils.a(applicationContext, "com.qiyuesuo.client.QiyuesuoClient", "getServiceUrl", new Class[0], new Object[0]);
            if (null != qysServiceUrl) {
                String[] parts = qysServiceUrl.split("\\.");
                if (parts.length > 1) {
                    String suffix = parts[parts.length - 1].replace("/", "");
                    return "https://gw.qiyuesuo." + suffix;
                } else {
                    return "https://gw.qiyuesuo.com";
                }
            } else {
                return "https://gw.qiyuesuo.com";
            }
        }
    }

    private static void initialCheck(ApplicationContext applicationContext) throws Exception {
        if (StringUtils.isAnyBlank(new CharSequence[]{getServiceUrl(applicationContext), getAccessKey(applicationContext), getAccessSecret(applicationContext)})) {
            throw new Exception("系统尚未激活，方法不可用");
        }
    }

    private static String getAccessKey(ApplicationContext applicationContext) {
        GlobalPropertiesCache globalPropertiesCache = (GlobalPropertiesCache)applicationContext.getBean(GlobalPropertiesCache.class);
        TokenProperties tokenProperties = globalPropertiesCache.get().getTokenProperties();
        return tokenProperties != null ? tokenProperties.getToken() : null;
    }

    private static String getAccessSecret(ApplicationContext applicationContext) {
        GlobalPropertiesCache globalPropertiesCache = (GlobalPropertiesCache)applicationContext.getBean(GlobalPropertiesCache.class);
        TokenProperties tokenProperties = globalPropertiesCache.get().getTokenProperties();
        return tokenProperties != null ? tokenProperties.getSecret() : null;
    }
}
