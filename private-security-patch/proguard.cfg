#-target 1.8
#
##-dontshrink
#
##-dontoptimize
#
##-ignorewarnings
#
##-dontskipnonpubliclibraryclasses
#
##-dontskipnonpubliclibraryclassmembers
#
##-dontusemixedcaseclassnames
#
##-allowaccessmodification
#
## -adaptclassstrings
#
##-keeppackagenames
#
##-useuniqueclassmembernames
#
## -keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,LocalVariable*Table,*Annotation,Synthetic,EnclosingMethod
#
## -keep interface * extends * {*;}
#
##-keepclassmembers class * {
##  @org.springframework.beans.factory.annotation.Autowired *;
##  @org.springframework.beans.factory.annotation.Value *;
##  @org.springframework.stereotype.Service *;
##  @org.springframework.stereotype.Component *;
##  @org.springframework.web.bind.annotation.PostMapping *;
##  @org.springframework.web.bind.annotation.DeleteMapping *;
##  @org.springframework.web.bind.annotation.RestController *;
##  @org.springframework.context.annotation.Configuration *;
##  @javax.annotation.Resource *;
##}
##
#-keep @org.springframework.context.annotation.Configuration class com.qiyuesuo.security.patch.config.PrivateSecurityConfig { *;}
#
## -keep class * implements java.io.Serializable {*;}
#
##-keepclassmembers enum *{
##   *;
##}
#-keep interface com.qiyuesuo.security.patch.SecurityPatchVersion{
#     *;
#}
#
##-keep class com.qiyuesuo.security.patch.config.** { *;}
#
#
#
## -keepclassmembers public class * { void set*(***); *** get*();}
#
##-dontwarn **
#
## 禁用调试信息
#-dontoptimize
#-dontobfuscate
#-dontnote
#-dontwarn
#
### 保留异常信息
##-keepattributes Exceptions
##
### 保留类名和成员名的结构
##-keepclassmembers class * {
##    <fields>;
##    <methods>;
##}
#
## 禁止优化和缩减
##-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
